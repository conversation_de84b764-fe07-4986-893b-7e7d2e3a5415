// Millionaire Quiz Game Logic
class MillionaireQuiz {
    constructor() {
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.timer = null;
        this.timeLeft = 15;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };
        this.lifelinesUsed = 0;
        this.correctAnswers = 0;
        this.totalExp = 0;
        this.questions = [];
        this.level = parseInt(document.body.dataset.level) || 3; // Default to level 3 for millionaire
        this.lives = 0;
        this.maxLives = 0;
        this.initializeElements();
        this.initializeEventListeners();
        this.loadQuestionsFromDatabase();
    }
    
    initializeElements() {
        // Modals
        this.instructionModal = document.getElementById('instruction-modal');
        this.gameArea = document.getElementById('game-area');
        this.resultsModal = document.getElementById('results-modal');
        this.audienceModal = document.getElementById('audience-modal');
        this.friendModal = document.getElementById('friend-modal');
        
        // Game elements
        this.questionText = document.getElementById('question-text');
        this.questionNum = document.getElementById('question-num');
        this.questionValue = document.getElementById('question-value');
        this.currentPrize = document.getElementById('current-prize');
        this.timeElement = document.getElementById('time');
        this.optionsContainer = document.getElementById('options-container');
        
        // Buttons
        this.startBtn = document.getElementById('start-btn');
        this.finalAnswerBtn = document.getElementById('final-answer-btn');
        this.walkAwayBtn = document.getElementById('walk-away-btn');
        this.playAgainBtn = document.getElementById('play-again-btn');
        this.backToMenuBtn = document.getElementById('back-to-menu-btn');
        
        // Lifelines
        this.fiftyFiftyBtn = document.getElementById('fifty-fifty');
        this.askAudienceBtn = document.getElementById('ask-audience');
        this.phoneFriendBtn = document.getElementById('phone-friend');

        // Host elements
        this.hostElement = document.getElementById('quiz-host');
        this.hostText = document.querySelector('.host-text');
        this.pikachuCharacter = document.querySelector('.pikachu-character');
        
        // Results
        this.resultTitle = document.getElementById('result-title');
        this.resultMessage = document.getElementById('result-message');
        this.livesElement = document.querySelector('.lives');
    }
    
    initializeEventListeners() {
        this.startBtn.addEventListener('click', () => this.startGame());
        this.finalAnswerBtn.addEventListener('click', () => this.submitFinalAnswer());
        this.walkAwayBtn.addEventListener('click', () => this.walkAway());
        this.playAgainBtn.addEventListener('click', () => this.resetGame());
        this.backToMenuBtn.addEventListener('click', () => this.backToMenu());
        
        // Lifelines
        this.fiftyFiftyBtn.addEventListener('click', () => this.useFiftyFifty());
        this.askAudienceBtn.addEventListener('click', () => this.useAskAudience());
        this.phoneFriendBtn.addEventListener('click', () => this.usePhoneFriend());
        
        // Modal close buttons
        document.getElementById('close-audience-modal').addEventListener('click', () => {
            this.audienceModal.style.display = 'none';
        });
        document.getElementById('close-friend-modal').addEventListener('click', () => {
            this.friendModal.style.display = 'none';
        });
        
        // Option selection
        this.optionsContainer.addEventListener('click', (e) => {
            if (e.target.closest('.option') && !e.target.closest('.option').classList.contains('eliminated')) {
                this.selectOption(e.target.closest('.option'));
            }
        });
    }
    
    async loadQuestionsFromDatabase() {
        try {
            const response = await fetch(`../../php/game.php?level=${this.level}`);
            const data = await response.json();

            if (data.success && data.data.length > 0) {
                // Convert database format to game format
                this.questions = data.data.map(item => ({
                    question: item.question_text,
                    options: [item.option1, item.option2, item.option3, item.option4],
                    correctValue: item.correct_answer_value // Use the actual value
                }));
                this.prizeAmounts = this.generatePrizeAmounts(this.questions.length);
                this.lives = Math.max(1, Math.floor(this.questions.length * 0.2));
                this.maxLives = this.lives;
                this.generatePrizeLadder();
                this.updateLivesDisplay();
                this.displayQuestion();
                console.log(`Loaded ${this.questions.length} questions from database`);
            } else {
                console.warn('No questions found in database, using fallback questions');
                this.loadFallbackQuestions();
            }
        } catch (error) {
            console.error('Error loading questions from database:', error);
            this.loadFallbackQuestions();
        }
    }

    generatePrizeAmounts(count) {
        // Classic Millionaire progression: double each time, big jump at the end
        let prizes = [];
        let value = 100;
        for (let i = 0; i < count; i++) {
            if (i === count - 1) {
                value = value * 5;
            } else if (i > 0) {
                value = value * 2;
            }
            prizes.push(value);
        }
        return prizes;
    }

    generatePrizeLadder() {
        const ladder = document.getElementById('ladder-horizontal');
        ladder.innerHTML = '';
        if (!this.questions || !this.prizeAmounts || this.questions.length === 0 || this.prizeAmounts.length === 0) {
            ladder.innerHTML = '<div style="color: #fff; padding: 10px;">No questions available.</div>';
            return;
        }
        for (let i = 0; i < this.questions.length; i++) {
            const div = document.createElement('div');
            div.className = 'prize-item-horizontal';
            div.dataset.level = i + 1;
            div.textContent = `$${this.prizeAmounts[i] ? this.prizeAmounts[i].toLocaleString() : '0'}`;
            ladder.appendChild(div);
        }
    }

    loadFallbackQuestions() {
        // Fallback questions if database fails
        this.questions = [
            // Questions 1-5: Easy ($100-$1,000)
            {
                question: "What is the capital of France?",
                options: ["London", "Berlin", "Paris", "Madrid"],
                correctValue: 2
            },
            {
                question: "Which planet is known as the Red Planet?",
                options: ["Venus", "Mars", "Jupiter", "Saturn"],
                correctValue: 1
            },
            {
                question: "What is 2 + 2?",
                options: ["3", "4", "5", "6"],
                correctValue: 1
            },
            {
                question: "Which animal is known as the 'King of the Jungle'?",
                options: ["Tiger", "Elephant", "Lion", "Leopard"],
                correctValue: 2
            },
            {
                question: "How many days are there in a week?",
                options: ["5", "6", "7", "8"],
                correctValue: 2
            },

            // Questions 6-10: Medium ($2,000-$32,000)
            {
                question: "Who painted the Mona Lisa?",
                options: ["Vincent van Gogh", "Pablo Picasso", "Leonardo da Vinci", "Michelangelo"],
                correctValue: 2
            },
            {
                question: "What is the chemical symbol for gold?",
                options: ["Go", "Gd", "Au", "Ag"],
                correctValue: 2
            },
            {
                question: "Which country has the most natural lakes?",
                options: ["Russia", "Canada", "Finland", "Sweden"],
                correctValue: 1
            },
            {
                question: "In which year did the Titanic sink?",
                options: ["1910", "1912", "1914", "1916"],
                correctValue: 1
            },
            {
                question: "What is the smallest prime number?",
                options: ["0", "1", "2", "3"],
                correctValue: 2
            },

            // Questions 11-15: Hard ($64,000-$1,000,000)
            {
                question: "Which programming language was created by Guido van Rossum?",
                options: ["Java", "Python", "C++", "JavaScript"],
                correctValue: 1
            },
            {
                question: "What is the speed of light in vacuum (approximately)?",
                options: ["299,792,458 m/s", "300,000,000 m/s", "299,000,000 m/s", "298,792,458 m/s"],
                correctValue: 0
            },
            {
                question: "Which element has the highest melting point?",
                options: ["Carbon", "Tungsten", "Rhenium", "Osmium"],
                correctValue: 1
            },
            {
                question: "In quantum mechanics, what principle states that you cannot simultaneously know both position and momentum of a particle?",
                options: ["Pauli Exclusion Principle", "Heisenberg Uncertainty Principle", "Schrödinger's Principle", "Planck's Principle"],
                correctValue: 1
            },
            {
                question: "What is the name of the theoretical boundary around a black hole beyond which nothing can escape?",
                options: ["Photon Sphere", "Ergosphere", "Event Horizon", "Singularity"],
                correctValue: 2
            }
        ];
        this.prizeAmounts = this.generatePrizeAmounts(this.questions.length);
        this.lives = Math.max(1, Math.floor(this.questions.length * 0.2));
        this.maxLives = this.lives;
        this.generatePrizeLadder();
        this.updateLivesDisplay();
        this.displayQuestion();
    }
    
    startGame() {
        this.instructionModal.style.display = 'none';
        this.gameArea.style.display = 'block';
        this.gameActive = true;
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.lifelinesUsed = 0;
        this.lives = this.maxLives;
        this.updateLivesDisplay();
        // Reset lifelines
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };
        this.updateLifelineButtons();
        this.displayQuestion();
        this.updatePrizeLadder();
        this.startTimer();
        // Special entrance animation for Pikachu
        this.hostEntrance();
    }
    
    displayQuestion() {
        if (!this.questions || !this.prizeAmounts || this.questions.length === 0 || this.prizeAmounts.length === 0) {
            this.questionText.textContent = 'No questions available.';
            this.questionNum.textContent = '0/0';
            this.questionValue.textContent = '0';
            return;
        }
        if (this.currentQuestion >= this.questions.length) {
            this.endGame(true, this.prizeAmounts[this.currentQuestion - 1] || 0);
            return;
        }
        const question = this.questions[this.currentQuestion];
        this.questionText.textContent = question.question;
        this.questionNum.textContent = `${this.currentQuestion + 1}/${this.questions.length}`;
        this.questionValue.textContent = this.prizeAmounts[this.currentQuestion] ? this.prizeAmounts[this.currentQuestion].toLocaleString() : '0';

        // Shuffle options and keep track of correct value
        const originalOptions = [...question.options];
        const shuffledOptions = this.shuffleArray([...originalOptions]);
        question._shuffledOptions = shuffledOptions;
        // Store the correct value for this shuffle
        question._shuffledCorrectValue = question.correctValue;

        // Update options
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach((option, index) => {
            const optionText = option.querySelector('.option-text');
            if (shuffledOptions[index] !== undefined) {
                optionText.textContent = shuffledOptions[index];
                option.setAttribute('data-value', shuffledOptions[index]);
                option.style.display = '';
                option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
                option.style.pointerEvents = 'auto';
                option.style.opacity = '';
            } else {
                // Hide or disable extra options
                optionText.textContent = '';
                option.setAttribute('data-value', '');
                option.style.display = 'none';
                option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
                option.style.pointerEvents = 'none';
                option.style.opacity = '';
            }
        });

        this.selectedAnswer = null;
        this.finalAnswerBtn.disabled = true;
        this.updatePrizeLadder();
        this.updateHostMessage();
    }
    
    selectOption(optionElement) {
        // Remove previous selection
        this.optionsContainer.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        // Select new option
        optionElement.classList.add('selected');
        this.selectedAnswer = optionElement.getAttribute('data-value');
        this.finalAnswerBtn.disabled = false;
    }
    
    startTimer() {
        this.timeLeft = 15;
        this.timeElement.textContent = this.timeLeft;
        
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.timeElement.textContent = this.timeLeft;
            
            if (this.timeLeft <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        if (this.selectedAnswer === null) {
            this.endGame(false, this.getGuaranteedMoney());
        } else {
            this.submitFinalAnswer();
        }
    }
    
    submitFinalAnswer() {
        if (this.selectedAnswer === null) return;
        this.finalAnswerBtn.disabled = true;
        this.stopTimer();
        this.gameActive = false;
        const question = this.questions[this.currentQuestion];
        // Use the shuffled correct value for comparison
        const isCorrect = this.selectedAnswer === question._shuffledCorrectValue;
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach(option => option.style.pointerEvents = 'none');
        setTimeout(() => {
            // Show correct answer
            options.forEach(option => {
                if (
                    option &&
                    option.style.display !== 'none' &&
                    option.getAttribute('data-value') &&
                    option.getAttribute('data-value') === question._shuffledCorrectValue
                ) {
                    option.classList.add('correct');
                }
            });
            if (isCorrect) {
                this.playSound('correct');
                this.hostReaction(true);
                // Flash the correct answer
                let flashCount = 0;
                const correctOption = Array.from(options).find(option =>
                    option &&
                    option.style.display !== 'none' &&
                    option.getAttribute('data-value') &&
                    option.getAttribute('data-value') === question._shuffledCorrectValue
                );
                const flashInterval = setInterval(() => {
                    if (correctOption) correctOption.style.opacity = flashCount % 2 === 0 ? '0.5' : '1';
                    flashCount++;
                    if (flashCount >= 6) {
                        clearInterval(flashInterval);
                        if (correctOption) correctOption.style.opacity = '1';
                    }
                }, 200);
            } else {
                const selectedOption = Array.from(options).find(option =>
                    option &&
                    option.style.display !== 'none' &&
                    option.getAttribute('data-value') &&
                    option.getAttribute('data-value') === this.selectedAnswer
                );
                if (selectedOption) selectedOption.classList.add('incorrect');
                this.playSound('wrong');
                this.hostReaction(false);
                this.lives--;
                this.updateLivesDisplay();
            }
            setTimeout(() => {
                if (isCorrect) {
                    this.correctAnswers++;
                    const expEarned = 10;
                    this.totalExp += expEarned;
                    this.saveExpToDatabase(expEarned);
                    this.currentQuestion++;
                    if (this.currentQuestion >= this.questions.length) {
                        this.playSound('success');
                        this.endGame(true, this.prizeAmounts[this.questions.length - 1]);
                    } else {
                        this.gameActive = true;
                        options.forEach(option => option.style.pointerEvents = 'auto');
                        this.finalAnswerBtn.disabled = true;
                        this.displayQuestion();
                        this.startTimer();
                    }
                } else {
                    if (this.lives > 0) {
                        this.currentQuestion++;
                        if (this.currentQuestion >= this.questions.length) {
                            this.endGame(false, this.prizeAmounts[this.questions.length - 1]);
                        } else {
                            this.gameActive = true;
                            options.forEach(option => option.style.pointerEvents = 'auto');
                            this.finalAnswerBtn.disabled = true;
                            this.displayQuestion();
                            this.startTimer();
                        }
                    } else {
                        this.endGame(false, this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0);
                    }
                }
            }, 1000);
        }, 1000);
    }
    
    getGuaranteedMoney() {
        let guaranteed = 0;
        for (let i = this.questions.length - 1; i >= 0; i--) {
            if (this.currentQuestion > i) {
                guaranteed = this.prizeAmounts[i];
                break;
            }
        }
        return guaranteed;
    }
    
    walkAway() {
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.endGame(true, currentWinnings, true);
    }
    
    updatePrizeLadder() {
        // Update horizontal prize ladder
        const prizeItems = document.querySelectorAll('.prize-item-horizontal');
        prizeItems.forEach((item, index) => {
            const  ca = parseInt(item.dataset.level);
            item.classList.remove('current', 'completed');

            if (level === this.currentQuestion + 1) {
                item.classList.add('current');
                // Scroll to current item
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            } else if (level <= this.currentQuestion) {
                item.classList.add('completed');
            }
        });

        // Update current prize display
        const currentWinnings = this.currentQuestion > 0 ? this.prizeAmounts[this.currentQuestion - 1] : 0;
        this.currentPrize.textContent = currentWinnings.toLocaleString();
    }

    // Host interaction methods
    updateHostMessage() {
        if (!this.hostText) return;
        // Defensive: avoid errors if prizeAmounts is undefined or empty
        let questionMessages;
        let prizeValue = 0;
        if (this.prizeAmounts && this.prizeAmounts.length > this.currentQuestion && typeof this.prizeAmounts[this.currentQuestion] === 'number') {
            prizeValue = this.prizeAmounts[this.currentQuestion];
        }
        if (this.currentQuestion < 5) {
            questionMessages = [
                "Let's start with an easy one! ⚡",
                "This should be simple! 😊",
                "Warming up time! 🔥",
                "Easy money coming up! 💰",
                "You've got this! 💪"
            ];
        } else if (this.currentQuestion < 10) {
            questionMessages = [
                "Getting tougher now! 🤔",
                "Think it through! 🧠",
                "This one's tricky! ⚡",
                prizeValue ? `$${prizeValue.toLocaleString()} question! 💰` : "Big question! 💰",
                "Stay focused! 👀"
            ];
        } else if (this.currentQuestion < 14) {
            questionMessages = [
                "Big money question! 💎",
                "This is getting serious! ⚡",
                "Almost there! 🚀",
                prizeValue ? `$${prizeValue.toLocaleString()} on the line! 💰` : "Big prize on the line! 💰",
                "You're doing amazing! ⭐"
            ];
        } else {
            questionMessages = [
                "MILLIONAIRE QUESTION! 👑",
                "This is it! The big one! 💎",
                "One million dollars! 🤑",
                "Make history! ⚡",
                "Pika believes in you! 💪"
            ];
        }
        const randomMessage = questionMessages[Math.floor(Math.random() * questionMessages.length)];
        this.hostText.textContent = randomMessage;
        // Add special styling for high-value questions
        if (this.currentQuestion >= 10) {
            this.hostText.style.color = '#ffd700';
            this.hostText.style.fontWeight = 'bold';
        } else {
            this.hostText.style.color = '#2c3e50';
            this.hostText.style.fontWeight = '600';
        }
    }

    hostReaction(isCorrect) {
        if (!this.pikachuCharacter) return;

        if (isCorrect) {
            // Happy reaction with enhanced animation
            this.pikachuCharacter.style.animation = 'none';
            this.pikachuCharacter.offsetHeight; // Trigger reflow
            this.pikachuCharacter.style.animation = 'celebration 0.8s ease-in-out 4';

            if (this.hostText) {
                let happyMessages;
                const prizeValue = this.prizeAmounts[this.currentQuestion - 1];

                if (this.currentQuestion <= 5) {
                    happyMessages = [
                        "Excellent! ⚡",
                        "Well done! 😊",
                        "Correct! 🎉",
                        "Perfect! ⭐",
                        "Pika pika! 💛"
                    ];
                } else if (this.currentQuestion <= 10) {
                    happyMessages = [
                        "Amazing work! 🔥",
                        "You're on fire! ⚡",
                        "Brilliant! 🌟",
                        `$${prizeValue.toLocaleString()} earned! 💰`,
                        "Keep it up! 🚀"
                    ];
                } else if (this.currentQuestion <= 14) {
                    happyMessages = [
                        "INCREDIBLE! 💎",
                        "You're unstoppable! ⚡",
                        "AMAZING! 🏆",
                        `$${prizeValue.toLocaleString()} WON! 💰`,
                        "Almost a millionaire! 👑"
                    ];
                } else {
                    happyMessages = [
                        "MILLIONAIRE! 👑💰",
                        "YOU DID IT! 🎉💎",
                        "PIKA PIKA CHAMPION! ⚡👑",
                        "LEGENDARY! 🏆⭐",
                        "ONE MILLION DOLLARS! 💰🎊"
                    ];
                }

                this.hostText.textContent = happyMessages[Math.floor(Math.random() * happyMessages.length)];
                this.hostText.style.color = '#ffd700';
                this.hostText.style.fontWeight = 'bold';
            }
        } else {
            // Sad reaction
            if (this.hostText) {
                const sadMessages = [
                    "Oh no... 😢",
                    "So close! 💔",
                    "Don't give up! 💪",
                    "You tried your best! ⭐",
                    "Pika... 😔",
                    "Better luck next time! 🍀"
                ];
                this.hostText.textContent = sadMessages[Math.floor(Math.random() * sadMessages.length)];
                this.hostText.style.color = '#e74c3c';
                this.hostText.style.fontWeight = '600';
            }
        }

        // Reset animation after reaction
        setTimeout(() => {
            if (this.pikachuCharacter) {
                this.pikachuCharacter.style.animation = 'hostBounce 4s ease-in-out infinite';
            }
        }, 1000);
    }

    hostEntrance() {
        if (!this.hostElement) return;

        // Start with Pikachu hidden and slide in
        this.hostElement.style.transform = 'translateX(-100px)';
        this.hostElement.style.opacity = '0';

        setTimeout(() => {
            this.hostElement.style.transition = 'all 1s ease-out';
            this.hostElement.style.transform = 'translateX(0)';
            this.hostElement.style.opacity = '1';

            // Welcome message
            if (this.hostText) {
                this.hostText.textContent = "Hi! I'm Pikachu! ⚡";
                this.hostText.style.color = '#ffd700';

                setTimeout(() => {
                    this.updateHostMessage();
                }, 500);
            }
        }, 500);
    }

    // Lifeline Methods
    useFiftyFifty() {
        if (!this.lifelines.fiftyFifty || !this.gameActive) return;

        this.lifelines.fiftyFifty = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();

        const question = this.questions[this.currentQuestion];
        const options = this.optionsContainer.querySelectorAll('.option');
        const incorrectOptions = [];

        // Find incorrect options
        options.forEach((option, index) => {
            if (index !== question.correctValue) {
                incorrectOptions.push(index);
            }
        });

        // Randomly eliminate 2 incorrect options
        const toEliminate = this.shuffleArray(incorrectOptions).slice(0, 2);
        toEliminate.forEach(index => {
            options[index].classList.add('eliminated');
        });

        this.playSound('correct'); // Use existing sound
    }

    useAskAudience() {
        if (!this.lifelines.askAudience || !this.gameActive) return;

        this.lifelines.askAudience = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();

        const question = this.questions[this.currentQuestion];
        const audienceBars = this.audienceModal.querySelectorAll('.audience-bar');

        // Generate audience percentages (biased toward correct answer)
        const percentages = this.generateAudiencePercentages(question.correctValue);

        audienceBars.forEach((bar, index) => {
            const barElement = bar.querySelector('.bar');
            const percentageElement = bar.querySelector('.percentage');

            setTimeout(() => {
                barElement.style.width = percentages[index] + '%';
                percentageElement.textContent = percentages[index] + '%';
            }, index * 200);
        });

        this.audienceModal.style.display = 'flex';
        this.playSound('correct');
    }

    usePhoneFriend() {
        if (!this.lifelines.phoneFreind || !this.gameActive) return;

        this.lifelines.phoneFreind = false;
        this.lifelinesUsed++;
        this.updateLifelineButtons();

        const question = this.questions[this.currentQuestion];
        const friendMessage = document.getElementById('friend-message');

        // Generate friend's advice (usually helpful but not always certain)
        const advice = this.generateFriendAdvice(question);

        friendMessage.textContent = "Your friend is thinking...";
        this.friendModal.style.display = 'flex';

        setTimeout(() => {
            friendMessage.textContent = advice;
        }, 2000);

        this.playSound('correct');
    }

    generateAudiencePercentages(correctIndex) {
        const percentages = [0, 0, 0, 0];

        // Give correct answer 40-70% of votes
        const correctPercentage = 40 + Math.random() * 30;
        percentages[correctIndex] = Math.round(correctPercentage);

        // Distribute remaining percentage among other options
        let remaining = 100 - percentages[correctIndex];
        for (let i = 0; i < 4; i++) {
            if (i !== correctIndex) {
                if (remaining > 0) {
                    const percentage = Math.random() * remaining;
                    percentages[i] = Math.round(percentage);
                    remaining -= percentages[i];
                }
            }
        }

        // Adjust for rounding errors
        const total = percentages.reduce((sum, p) => sum + p, 0);
        if (total !== 100) {
            percentages[correctIndex] += (100 - total);
        }

        return percentages;
    }

    generateFriendAdvice(question) {
        const options = ['A', 'B', 'C', 'D'];
        const correctOption = options[question.correctValue];

        const adviceTemplates = [
            `I'm pretty sure it's ${correctOption}. That sounds right to me.`,
            `Hmm, I think it might be ${correctOption}, but I'm not 100% certain.`,
            `I remember learning about this - I believe it's ${correctOption}.`,
            `My gut feeling says ${correctOption}. Hope that helps!`,
            `I'd go with ${correctOption} if I were you.`
        ];

        // 80% chance of giving correct advice
        if (Math.random() < 0.8) {
            return adviceTemplates[Math.floor(Math.random() * adviceTemplates.length)];
        } else {
            // 20% chance of being uncertain or giving wrong advice
            const wrongOption = options[Math.floor(Math.random() * 4)];
            return `I'm not really sure about this one. Maybe ${wrongOption}? Sorry, I wish I could be more help.`;
        }
    }

    updateLifelineButtons() {
        this.fiftyFiftyBtn.disabled = !this.lifelines.fiftyFifty;
        this.askAudienceBtn.disabled = !this.lifelines.askAudience;
        this.phoneFriendBtn.disabled = !this.lifelines.phoneFreind;
    }

    endGame(won, winnings, walkedAway = false) {
        this.stopTimer();
        this.gameActive = false;
        // Calculate score percentage
        const scorePercent = Math.round((this.correctAnswers / this.questions.length) * 100);
        // Star logic: 0 star if <80, 1 star if 80-86, 2 stars if 86-95, 3 stars if 96-100
        let stars = 0;
        if (scorePercent >= 96) stars = 3;
        else if (scorePercent >= 86) stars = 2;
        else if (scorePercent >= 80) stars = 1;
        // Update stars display
        const starsElement = document.getElementById('result-stars');
        if (starsElement) {
            starsElement.innerHTML = '';
            for (let i = 0; i < 3; i++) {
                const star = document.createElement('span');
                star.className = i < stars ? 'star' : 'star empty';
                star.textContent = i < stars ? '⭐' : '☆';
                starsElement.appendChild(star);
            }
        }
        // Update exp display
        const expText = document.querySelector('.exp-text');
        expText.textContent = `+${this.totalExp} EXP`;
        // Update result display
        if (walkedAway) {
            this.resultTitle.textContent = "You Walked Away!";
            this.resultMessage.textContent = `You walked away with $${winnings.toLocaleString()}!`;
        } else if (won && winnings === 1000000) {
            this.resultTitle.textContent = "🎉 MILLIONAIRE! 🎉";
            this.resultMessage.textContent = "Congratulations! You won $1,000,000!";
            if (this.hostText) {
                this.hostText.textContent = "PIKA PIKA! You did it!";
            }
            if (this.pikachuCharacter) {
                this.pikachuCharacter.style.animation = 'celebration 1s ease-in-out infinite';
            }
            setTimeout(() => {
                this.resultsModal.querySelector('.modal-content').classList.add('millionaire-celebration');
                this.createConfetti();
            }, 500);
        } else if (won) {
            this.resultTitle.textContent = "Congratulations!";
            this.resultMessage.textContent = `You won $${winnings.toLocaleString()}!`;
        } else {
            this.resultTitle.textContent = "Game Over!";
            this.resultMessage.textContent = `You leave with $${winnings.toLocaleString()}.`;
        }
        // Save final game data to database
        this.saveFinalGameData(this.correctAnswers, stars);
        setTimeout(() => {
            this.resultsModal.style.display = 'flex';
        }, 500);
    }

    createConfetti() {
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.animationDelay = Math.random() * 3 + 's';
                document.body.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 3000);
            }, i * 100);
        }
    }

    resetGame() {
        this.resultsModal.style.display = 'none';
        this.gameArea.style.display = 'none';
        this.instructionModal.style.display = 'flex';

        // Reset all game state
        this.currentQuestion = 0;
        this.selectedAnswer = null;
        this.gameActive = false;
        this.lifelinesUsed = 0;
        this.correctAnswers = 0;
        this.totalExp = 0;
        this.lifelines = {
            fiftyFifty: true,
            askAudience: true,
            phoneFreind: true
        };

        // Reset option elements completely
        const options = this.optionsContainer.querySelectorAll('.option');
        options.forEach(option => {
            // Remove all game-related CSS classes
            option.classList.remove('selected', 'correct', 'incorrect', 'eliminated');
            // Restore pointer events
            option.style.pointerEvents = 'auto';
            // Clear any inline opacity styles
            option.style.opacity = '';
        });

        // Reset final answer button
        this.finalAnswerBtn.disabled = true;

        // Reset lifeline buttons
        this.updateLifelineButtons();

        // Reset prize ladder
        const prizeItems = document.querySelectorAll('.prize-item-horizontal');
        prizeItems.forEach(item => {
            item.classList.remove('current', 'completed');
        });

        // Reset current prize display
        this.currentPrize.textContent = '0';

        // Reset timer display
        this.timeElement.textContent = '15';

        // Reset host state
        if (this.hostElement) {
            this.hostElement.style.transform = '';
            this.hostElement.style.opacity = '';
            this.hostElement.style.transition = '';
        }

        // Reset Pikachu animation
        if (this.pikachuCharacter) {
            this.pikachuCharacter.style.animation = 'hostBounce 4s ease-in-out infinite';
        }

        // Reset host text
        if (this.hostText) {
            this.hostText.textContent = "Here's your question!";
            this.hostText.style.color = '#2c3e50';
            this.hostText.style.fontWeight = '600';
        }

        this.stopTimer();
    }

    backToMenu() {
        window.location.href = '../../html/mainpage.html';
    }

    // Database save methods
    saveExpToDatabase(expEarned) {
        fetch('../../php/savetodb.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expEarned: expEarned,
                score: 0, // We'll update the final score at the end
                starsCount: 0, // We'll update the stars at the end
                level: this.level
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Failed to save exp:', data.error);
            }
        })
        .catch(error => {
            console.error('Error saving exp:', error);
        });
    }

    saveFinalGameData(score, stars) {
        fetch('../../php/savetodb.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expEarned: 0, // We already sent exp per question
                score: score,
                starsCount: stars,
                level: this.level
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Failed to save final game data:', data.error);
            }
        })
        .catch(error => {
            console.error('Error saving final game data:', error);
        });
    }

    // Utility methods
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    playSound(type) {
        // Use existing sound files if available
        try {
            let audio;
            switch(type) {
                case 'correct':
                    audio = new Audio('../../sounds/correct.mp3');
                    break;
                case 'wrong':
                    audio = new Audio('../../sounds/wrong.mp3');
                    break;
                case 'success':
                    audio = new Audio('../../sounds/success.mp3');
                    break;
                default:
                    return;
            }
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Audio play failed:', e));
        } catch (e) {
            console.log('Audio not available:', e);
        }
    }

    updateLivesDisplay() {
        if (!this.livesElement) {
            // Create lives display in header if not present
            const gameInfo = document.querySelector('.game-info');
            this.livesElement = document.createElement('div');
            this.livesElement.className = 'lives';
            gameInfo.appendChild(this.livesElement);
        }
        this.livesElement.innerHTML = `Lives: ${'❤️'.repeat(this.lives)} <span style="color:#ffd700">(${this.lives}/${this.maxLives})</span>`;
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new MillionaireQuiz();
    // Show the instruction modal initially
    game.instructionModal.style.display = 'flex';
    game.gameArea.style.display = 'none'; // Hide game area while modal is open
});
