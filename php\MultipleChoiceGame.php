<?php
session_start();
header('Content-Type: application/json');

// Get the POST data
$data = json_decode(file_get_contents('php://input'), true);

// Validate required data
// if (!isset($_SESSION['userId'], $data['score'], $data['starsCount'], $data['level'])) {
//     echo json_encode(['success' => false, 'error' => 'Missing required data']);
//     exit;
// }

$score = $data['score'];
$star = $data['starsCount'];
echo $userId = $_SESSION['userId'];
$level = $data['level'];
$expEarned = $data['expEarned'] ?? 0;
$newLevel = $level + 1;

try {
    $pdo = new PDO('mysql:host=localhost;dbname=dbfunconnect', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Start transaction
    $pdo->beginTransaction();
    
    // Check if record exists for current level
    $stmt = $pdo->prepare('SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND levelID = ?');
    $stmt->execute([$userId, $level]);
    $exists = $stmt->fetchColumn();

    // // Validate expEarned is numeric
    //$expEarned = is_numeric($data['expEarned'] ?? 0) ? (int)$data['expEarned'] : 0;
    
    // Check user_tier record first
    $stme = $pdo->prepare('SELECT COUNT(*) FROM user_tier WHERE user_id = ?');
    $stme->execute([$userId]);
    $userExp = $stme->fetchColumn();
    
    if ($userExp) {
        // Record exists - update it    
        $newExp = $userExp + $expEarned;
        $stme = $pdo->prepare('UPDATE user_tier SET userExp = ? WHERE user_id = ?');
        $stme->execute([$expEarned, $userId]);
    } else {

        // No record exists - initialize with expEarned
        $newExp = $expEarned;
        $stme = $pdo->prepare('INSERT INTO user_tier (user_id, userExp) VALUES (?, ?)');
        $stme->execute([$userId, $expEarned]);
        echo "tae";
    }

    
    if ($exists) {
        // Update existing record for current level
        $stmt = $pdo->prepare('UPDATE user_levels SET levelScore = ?, levelStar = ? WHERE user_id = ? AND levelID = ?');
        $stmt->execute([$score, $star, $userId, $level]);
    } else {
        // Insert new record for current level
        $stmt = $pdo->prepare('INSERT INTO user_levels (levelScore, levelStar, user_id, levelID) VALUES (?, ?, ?, ?)');
        $stmt->execute([$score, $star, $userId, $level]);
    }
    
    // Only unlock next level if user achieved at least 1 star
    if ($star >= 1) {
        // Check if next level exists in user's records
        $stmt = $pdo->prepare('SELECT COUNT(*) FROM user_levels WHERE user_id = ? AND levelID = ?');
        $stmt->execute([$userId, $newLevel]);
        $nextLevelExists = $stmt->fetchColumn();

        if ($nextLevelExists) {
            // Update isUnlocked for next level if record exists
            $stmt = $pdo->prepare('UPDATE user_levels SET isUnlocked = 1 WHERE user_id = ? AND levelID = ?');
            $stmt->execute([$userId, $newLevel]);
        } else {
            // Insert new record for next level with isUnlocked = 1
            $stmt = $pdo->prepare('INSERT INTO user_levels (user_id, levelID, isUnlocked) VALUES (?, ?, 1)');
            $stmt->execute([$userId, $newLevel]);
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode(['success' => true]);
    echo json_encode(['success' => true, 'newExp' => $newExp]); // Debug output
} catch (PDOException $e) {
    // Rollback on error
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}