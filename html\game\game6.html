<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="Robot Matching Quiz - Drag and drop answers to save the robot!">
    <title>Robot Matching Quiz</title>
    <link rel="stylesheet" href="../../css/style_hangman.css">
    <link rel="stylesheet" href="../../css/style_matching.css">
</head>
<body data-level="2">
    <div class="game-container">
        <!-- Instruction Modal -->
        <div class="modal" id="instruction-modal" role="dialog" aria-labelledby="instruction-title">
            <div class="modal-content">
                <h2 id="instruction-title">Matching Challenge</h2>
                <div class="instructions">
                    <p>Match each term with its correct definition!</p>
                    <ul>
                        <li>Drag the correct answer from the pool to the drop zone</li>
                        <li>Each correct match earns points and builds your streak</li>
                        <li>Use keyboard navigation: Tab to focus, Enter/Space to select</li>
                        <li>Answer all questions to achieve the highest score</li>
                        <li>Use power-ups wisely to boost your performance</li>
                    </ul>
                </div>
                <button id="start-btn" aria-label="Start Matching Challenge">Start Challenge</button>
            </div>
        </div>

        <!-- Game Area -->
        <div class="game-area" id="game-area" style="display: none;">
            <div class="quiz-section">
                <div class="header">
                    <div class="timer" aria-label="Time remaining">⏱️ <span id="time">45</span>s</div>
                    <div class="lives" aria-label="Lives remaining">❤️ <span id="lives"></span></div>
                    <div class="question-counter" aria-label="Question progress">
                        <span id="current-question">1</span> / <span id="total-questions">10</span>
                    </div>
                    <div class="volume-control" aria-label="Sound effects volume">
                        <label for="volume-slider">🔊</label>
                        <input type="range" id="volume-slider" min="0" max="100" value="30" aria-label="Adjust sound volume">
                        <span id="volume-value">30%</span>
                    </div>
                </div>

                <!-- Answer Pool Section -->
                <div class="answer-pool-section">
                    <h3 class="pool-title">Available Answers</h3>
                    <div class="answer-pool" id="answer-pool" role="region" aria-label="Available answer options">
                        <!-- Answer options will be populated here -->
                    </div>
                </div>

                <!-- Question Section -->
                <div class="question-section">
                    <div class="question" id="question" role="heading" aria-level="3"></div>
                    <div class="drop-zones" id="drop-zones" role="region" aria-label="Drop zones for answers">
                        <!-- Drop zones will be populated here -->
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="navigation-buttons">
                    <button class="nav-btn" id="next-btn" disabled aria-label="Next question">Next →</button>
                </div>

                <!-- Power-up Buttons -->
                <div class="power-ups">
                    <button id="addTimeBtn" class="power-up-btn add-time">
                        <span class="power-up-icon">⏱️</span>
                        <span class="power-up-text">+15 Time</span>
                    </button>
                    <button class="power-up-btn" id="show-answer" aria-label="Show correct answer">
                        <span class="power-up-icon">💡</span>
                        <span class="power-up-text">Show Answer</span>
                    </button>
                </div>
            </div>
            
            <!-- Score Display Section -->
            <div class="score-section">
                <div class="score-display">
                    <div class="score-item">
                        <span class="score-label">Score</span>
                        <span class="score-value" id="current-score">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">Correct</span>
                        <span class="score-value" id="correct-count">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">Streak</span>
                        <span class="score-value" id="streak-count">0</span>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
            </div>
        </div>

        <!-- Results Modal -->
        <div class="modal results-modal" id="results-modal" role="dialog" aria-labelledby="result-title" style="display: none;">
            <div class="modal-content">
                <h2 id="result-title" class="result-title">Game Over!</h2>
                <div id="result-stars" class="stars" aria-label="Star rating"></div>
                <p id="result-message" class="result-message">You won $0!</p>
                <div class="exp-container">
                    <span class="exp-icon">✨</span>
                    <span class="exp-text">+0 EXP</span>
                </div>
                <div class="modal-buttons">
                    <button class="replay-btn" id="play-again-btn">PLAY AGAIN</button>
                    <button class="main-menu-btn" id="back-to-menu-btn">MAIN MENU</button>
                </div>
            </div>
        </div>

    </div>

    <script src="../../js/matching-quiz.js"></script>
</body>
</html>